import {
  UserListResponseType,
  UserDetailResponseType,
  UserStatusChangeResponseType,
  UserApprovalResponseType,
  OperatorApprovalResponseType,
  ComprehensiveUserManagementResponseType,
  BulkOperationResponseType,
} from './types';

// Response transformers - convert backend data to frontend format
export const transformUserListResponse = (data: any): UserListResponseType => {
  // Handle different response structures
  const users = data.data?.users || data.users || [];
  const backendPagination = data.data?.pagination || data.pagination || {
    total: users.length,
    page: 0,
    limit: users.length,
    totalPages: 1,
  };

  // Convert backend 0-based pagination to frontend 1-based pagination
  const pagination = {
    total: backendPagination.total,
    page: (backendPagination.page || 0) + 1, // Convert 0-based to 1-based
    limit: backendPagination.limit || 20,
    pages: backendPagination.totalPages || Math.ceil((backendPagination.total || 0) / (backendPagination.limit || 20)),
  };

  return {
    success: data.success ?? true,
    data: {
      users: users.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        approval_status: user.approval_status,
        created_at: user.created_at,
        updated_at: user.updated_at,
      })),
      pagination,
    },
    message: data.message,
  };
};

export const transformUserDetailResponse = (data: any): UserDetailResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        name: data.data.user.name,
        email: data.data.user.email,
        phone: data.data.user.phone,
        user_type: data.data.user.user_type,
        status: data.data.user.status,
        approval_status: data.data.user.approval_status,
        created_at: data.data.user.created_at,
        updated_at: data.data.user.updated_at,
        address: data.data.user.address,
        city: data.data.user.city,
        country: data.data.user.country,
        profile_image: data.data.user.profile_image,
        documents: data.data.user.documents,
        vehicles: data.data.user.vehicles,
        access_points: data.data.user.access_points,
      },
    },
    message: data.message,
  };
};

export const transformUserStatusChangeResponse = (data: any): UserStatusChangeResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        status: data.data.user.status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformUserApprovalResponse = (data: any): UserApprovalResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        approval_status: data.data.user.approval_status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformOperatorApprovalResponse = (data: any): OperatorApprovalResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        user_type: data.data.user.user_type,
        approval_status: data.data.user.approval_status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformComprehensiveUserManagementResponse = (data: any): ComprehensiveUserManagementResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        name: data.data.user.name,
        email: data.data.user.email,
        phone: data.data.user.phone,
        user_type: data.data.user.user_type,
        status: data.data.user.status,
        approval_status: data.data.user.approval_status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformBulkOperationResponse = (data: any): BulkOperationResponseType => {
  return {
    success: data.success,
    data: {
      processed: data.data.processed,
      successful: data.data.successful,
      failed: data.data.failed,
      users: data.data.users.map((user: any) => ({
        id: user.id,
        success: user.success,
        message: user.message,
      })),
    },
    message: data.message,
  };
};
