/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable complexity */
/**
 * Unified handler for user management operations
 * Handles list, detail, status change, approval, and bulk operations
 */
import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../src/lib/axios';
import { API_ENDPOINT } from '../../src/data/api-endpoints';
import { handleBackendError } from '../../src/utils/handle-backend-error';
import {
  returnUserListParams,
  transformUserListResponse,
  transformUserDetailResponse,
  transformUserStatusChangeRequest,
  transformUserStatusChangeResponse,
  transformUserApprovalRequest,
  transformUserApprovalResponse,
  transformApprovalRequest,
  transformOperatorApprovalResponse,
  transformComprehensiveUserManagementRequest,
  transformComprehensiveUserManagementResponse,
  transformBulkOperationRequest,
  transformBulkOperationResponse,
} from '../../src/requests/admin-users';
import { getJwt } from '../../src/utils';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id: userId } = req.query;

  if (!token) {
    return res.status(401).json({
      success: false,
      error: {
        type: 'AUTHENTICATION_ERROR',
        message: 'No token provided',
      },
    });
  }

  // Get user detail by ID
  if (userId && userId !== 'undefined' && req.method === 'GET') {
    try {
      const response = await BACKEND_API.get(API_ENDPOINT.users.list, {
        params: {
          id: userId,
          detail: 'true',
          _t: Date.now(),
        },
        headers: {
          Authorization: token,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      });

      const transformedData = transformUserDetailResponse(response.data);
      return res.status(200).json(transformedData);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to fetch user details';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Get users list
  if (req.method === 'GET') {
    try {
      const params = returnUserListParams(req);

      // Debug: Log the parameters being sent
      console.log('Parameters being sent to backend:', params);
      console.log('Original query params:', req.query);

      const response = await BACKEND_API.get(API_ENDPOINT.users.list, {
        params,
        headers: { Authorization: token },
      });

      // Debug: Log the response structure
      console.log('Backend response structure:', JSON.stringify(response.data, null, 2));

      const transformedData = transformUserListResponse(response.data);
      return res.status(200).json(transformedData);
    } catch (error: any) {
      console.log('Users API error:', error.response?.data || error.message);
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to fetch users';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Update user status
  if (req.method === 'PUT' && req.body.operation === 'status') {
    try {
      const transformedData = transformUserStatusChangeRequest(req.body);
      const response = await BACKEND_API.put(API_ENDPOINT.users.status, transformedData, {
        headers: { Authorization: token },
      });

      const transformedResponse = transformUserStatusChangeResponse(response.data);
      return res.status(200).json(transformedResponse);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to update user status';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // User approval
  if (req.method === 'PUT' && req.body.operation === 'approval') {
    try {
      const transformedData = transformUserApprovalRequest(req.body);
      const response = await BACKEND_API.put(API_ENDPOINT.users.approval, transformedData, {
        headers: { Authorization: token },
      });

      const transformedResponse = transformUserApprovalResponse(response.data);
      return res.status(200).json(transformedResponse);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to approve user';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Operator approval
  if (req.method === 'POST' && req.body.operation === 'operator-approval') {
    try {
      const transformedData = transformApprovalRequest(req.body);
      const response = await BACKEND_API.post(API_ENDPOINT.users.approval, transformedData, {
        headers: { Authorization: token },
      });

      const transformedResponse = transformOperatorApprovalResponse(response.data);
      return res.status(200).json(transformedResponse);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to change operator approval';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Comprehensive user management
  if (req.method === 'PUT' && req.body.operation === 'management') {
    try {
      const transformedData = transformComprehensiveUserManagementRequest(req.body);
      const response = await BACKEND_API.put(API_ENDPOINT.users.management, transformedData, {
        headers: { Authorization: token },
      });

      const transformedResponse = transformComprehensiveUserManagementResponse(response.data);
      return res.status(200).json(transformedResponse);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to manage user';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Bulk operations
  if (req.method === 'POST' && (req.body.operation === 'bulk-approve' || req.body.operation === 'bulk-reject')) {
    try {
      const transformedData = transformBulkOperationRequest(req.body);
      const endpoint = req.body.operation === 'bulk-approve'
        ? API_ENDPOINT.users.bulkApprove
        : API_ENDPOINT.users.bulkReject;

      const response = await BACKEND_API.post(endpoint, transformedData, {
        headers: { Authorization: token },
      });

      const transformedResponse = transformBulkOperationResponse(response.data);
      return res.status(200).json(transformedResponse);
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || 'Failed to perform bulk operation';

      return res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  }

  // Method not allowed
  return res.status(405).json({
    success: false,
    error: {
      message: 'Method not allowed',
      code: 405,
    },
  });
}

export default handler;
